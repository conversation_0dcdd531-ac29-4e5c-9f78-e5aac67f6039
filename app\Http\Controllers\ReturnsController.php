<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ReturnsService;
use Illuminate\Support\Facades\Validator;

class ReturnsController extends Controller
{
    protected $returnsService;

    public function __construct(ReturnsService $returnsService)
    {
        $this->returnsService = $returnsService;
    }

    /**
     * List all returns
     */
    public function index(Request $request)
    {
        $filters = $request->only(['status', 'user_id', 'order_id']);
        $result = $this->returnsService->getReturns($request, $filters);

        $status = $result['success'] ? 200 : ($result['status'] ?? 500);
        return response()->json($result, $status);
    }

    /**
     * Show a specific return
     */
    public function show(Request $request, $id)
    {
        $result = $this->returnsService->getReturn($request, $id);

        $status = $result['success'] ? 200 : ($result['status'] ?? 500);
        return response()->json($result, $status);
    }

    /**
     * Create a new return
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|integer|exists:orders,id',
            'return_reason' => 'required|string|max:1000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max per image
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->get('user');
        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User authentication required'
            ], 401);
        }

        $data = $validator->validated();
        $images = $request->file('images', []);

        $result = $this->returnsService->createReturn($data, $images, $user);

        $status = $result['success'] ? 201 : ($result['status'] ?? 500);
        return response()->json($result, $status);
    }

    /**
     * Update return status
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:pending,approved,rejected,processed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->get('user');
        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User authentication required'
            ], 401);
        }

        $result = $this->returnsService->updateReturnStatus($id, $request->status, $user);

        $status = $result['success'] ? 200 : ($result['status'] ?? 500);
        return response()->json($result, $status);
    }

    /**
     * Delete a return
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->get('user');
        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User authentication required'
            ], 401);
        }

        $result = $this->returnsService->deleteReturn($id, $user);

        $status = $result['success'] ? 200 : ($result['status'] ?? 500);
        return response()->json($result, $status);
    }
}
