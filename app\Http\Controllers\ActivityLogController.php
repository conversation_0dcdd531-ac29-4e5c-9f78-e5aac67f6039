<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ActivityLogService;

class ActivityLogController extends Controller
{
    protected $activityLogService;

    public function __construct(ActivityLogService $activityLogService)
    {
        $this->activityLogService = $activityLogService;
    }

    /**
     * Get recent activity logs
     */
    public function index(Request $request)
    {
        $user = $request->get('user');
        $limit = $request->get('limit', 50);

        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User authentication required'
            ], 401);
        }

        $logs = $this->activityLogService->getRecentLogs($limit, $user);

        return response()->json([
            'response' => 'success',
            'data' => $logs
        ]);
    }

    /**
     * Get activity logs for current user
     */
    public function myLogs(Request $request)
    {
        $user = $request->get('user');
        $limit = $request->get('limit', 50);

        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User authentication required'
            ], 401);
        }

        $logs = $this->activityLogService->getActorLogs($user, $limit);

        return response()->json([
            'response' => 'success',
            'data' => $logs
        ]);
    }

    /**
     * Search activity logs
     */
    public function search(Request $request)
    {
        $user = $request->get('user');
        $search = $request->get('search');
        $limit = $request->get('limit', 50);

        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User authentication required'
            ], 401);
        }

        if (!$search) {
            return response()->json([
                'response' => 'error',
                'message' => 'Search term is required'
            ], 422);
        }

        $logs = $this->activityLogService->searchLogs($search, $user, $limit);

        return response()->json([
            'response' => 'success',
            'data' => $logs
        ]);
    }
}
