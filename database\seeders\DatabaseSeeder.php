<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
{
    dd('Seeder running'); // Add this temporarily

    DB::table('tenants')->delete();

    Tenant::insert([
        // your tenants here...
        [
            'name' => 'Tenant 1',
            'subdomain' => 'tenant1',
            'domain' => 'tenant1.example.com',
        ],
        [
            'name' => 'Tenant 2',
            'subdomain' => 'tenant2',
            'domain' => 'tenant2.example.com',
        ],
    ]);
}

}