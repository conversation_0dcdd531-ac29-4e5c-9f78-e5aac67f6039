<?php
namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use App\Services\AuthService;
use App\Services\ValidationService;

class AuthController extends Controller
{
    protected $authService;
    protected $validationService;

    public function __construct(AuthService $authService, ValidationService $validationService)
    {
        $this->authService = $authService;
        $this->validationService = $validationService;
    }

    public function register(Request $request)
    {
        $tenant = $request->get('tenant');

        $validation = $this->validationService->validateUserRegistration($request->all(), $tenant->id);

        if (!$validation['success']) {
            return response()->json([
                'response' => 'error',
                'message' => $validation['message'],
                'errors' => $validation['errors']
            ], 422);
        }

        $result = $this->authService->registerUser($validation['data'], $tenant);

        return response()->json($result, 201);
    }



    public function registerTenant(Request $request)
    {
        $validation = $this->validationService->validateTenantRegistration($request->all());

        if (!$validation['success']) {
            return response()->json([
                'response' => 'error',
                'message' => $validation['message'],
                'errors' => $validation['errors']
            ], 422);
        }

        $result = $this->authService->registerTenant($validation['data']);

        return response()->json($result, 201);
    }

    public function login(Request $request)
    {
        $validation = $this->validationService->validateUserLogin($request->all());

        if (!$validation['success']) {
            return response()->json([
                'response' => 'error',
                'message' => $validation['message'],
                'errors' => $validation['errors']
            ], 422);
        }

        $result = $this->authService->loginUser($validation['data']);

        $status = $result['response'] === 'success' ? 200 : 401;
        return response()->json($result, $status);
    }



    public function logout(Request $request)
    {
        $result = $this->authService->logoutUser();

        $status = $result['response'] === 'success' ? 200 : 500;
        return response()->json($result, $status);
    }

    public function refresh(Request $request)
    {
        $result = $this->authService->refreshToken();

        $status = $result['response'] === 'success' ? 200 : 401;
        return response()->json($result, $status);
    }

    public function profile(Request $request)
    {
        $user = $request->get('user');

        $result = $this->authService->getUserProfile($user);

        return response()->json($result);
    }
}
