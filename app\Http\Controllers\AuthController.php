<?php
namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    public function register(Request $request)
    {
        try{
            $tenant = $request->get('tenant');
            $data = $this->validateRegistration($request);

            $user = $tenant->users()->create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
            ]);

            return response()->json([
                'response' => 'success',
                'message' => 'User created',

                'result' => $user,
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'response' => 'error',
                'message'   => $e->errors(),
            ], 422);
        }
    }



public function registerTenant(Request $request)
{
    $validator = Validator::make($request->all(), [
        'name' => 'required|string|max:255',
        'domain' => 'required|string|max:255|unique:tenants,domain',
        'logo' => 'nullable|string|max:255',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'response' => 'error',
            'message' => 'Validation failed',
            'errors' => $validator->errors()
        ], 422);
    }

    $data = $validator->validated();

    $tenant = Tenant::create([
        'name' => $data['name'],
        'domain' => $data['domain'],
        'logo' => $data['logo'] ?? 'logos/default.png',
        'token' => generate_strong_token(64),
    ]);

    return response()->json([
        'response' => 'success',
        'result' => $tenant,
    ], 201);
}

public function login(Request $request)
{
    $data = $this->validateLogin($request);

    $user = User::where('email', $data['email'])->first();

    // Check if user exists and password matches
    if (! $user || ! is_string($user->password) || ! Hash::check($data['password'], $user->password)) {
        return response()->json([
            'response' => 'error',
            'message' => 'Invalid credentials',
        ], 401);
    }

    // Generate new token
    $user->api_token = generate_strong_token(60);
    $user->save();
    $result= [
        //add tenant name and logo
        'tenant' => $user->tenant->name,
        'logo' => $user->tenant->logo,
        'name' => $user->name,
        'email' => $user->email,
        'warehouses' => $user->tenant->warehouses()
            ->where('status', 'active')
            ->get()
            ->map(function ($warehouse) {
                return [
                    'id' => $warehouse->id,
                    'name' => $warehouse->name,
                    'location' => $warehouse->location,
                    'collections' => $warehouse->collections()->count(),
                ];
            }),
    ];
    return response()->json([
        'response' => 'success',
        'result' => $result,
        'token' => $user->api_token,
    ]);
}



    public function logout(Request $request)
    {
        $user = $request->get('user');
        $user->api_token = null;
        $user->save();

        return response()->json(['message' => 'Logged out']);
    }

    // 🔒 PRIVATE VALIDATION HELPERS

    private function validateRegistration(Request $request): array
    {
        $tenantId = $request->get('tenant')->id;

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users')->where(function ($query) use ($tenantId) {
                    return $query->where('tenant_id', $tenantId);
                }),
            ],
            'password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withMessages($validator->errors()->toArray());
        }

        return $validator->validated();
    }

    private function validateLogin(Request $request): array
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withMessages($validator->errors()->toArray());
        }

        return $validator->validated();
    }
}
