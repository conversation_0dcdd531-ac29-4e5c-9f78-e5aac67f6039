<?php

namespace App\Http\Controllers;

use App\Models\Warehouse;
use Illuminate\Http\Request;

class WarehouseController extends Controller
{

    /**
     * List all warehouses for the current tenant or return one by ID.
     */
    public function index(Request $request)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');
        $id = $request->query('id');

        $query = ($user ? $user->tenant : $tenant)
            ->warehouses()
            ->withCount('collections');

        if ($user) {
            $query->where('status', 'active');
        }

        // Fetch single warehouse
        if ($id) {
            $warehouse = $query->findOrFail($id);

            if ($user) {
                return response()->json([
                    'id' => $warehouse->id,
                    'name' => $warehouse->name,
                    'location' => $warehouse->location,
                    'collections' => $warehouse->collections->count(),
                ], 200);
            }

            return response()->json($warehouse, 200);
        }

        // Fetch all warehouses
        $warehouses = $query->get();

        if ($user) {
            $warehouses = $warehouses->map(function ($warehouse) {
                return [
                    'id' => $warehouse->id,
                    'name' => $warehouse->name,
                    'location' => $warehouse->location,
                    'collections' => $warehouse->collections->count(),
                ];
            })->values();
        }

        return response()->json($warehouses, 200);
    }


    /**
     * Create a new warehouse for the current tenant.
     */
    public function store(Request $request)
    {
        $tenant = $request->get('tenant');

        $data = $request->validate([
            'name' => 'required|string|max:255',
            'location' => 'string|max:255',
        ]);

        $warehouse = $tenant->warehouses()->create($data);

        return response()->json($warehouse, 201);
    }



    /**
     * Update an existing warehouse.
     */
    public function update(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $warehouse = $tenant->warehouses()->findOrFail($id);

        $data = $request->validate([
            'name' => 'sometimes|string|max:255',
            'location' => 'sometimes|string|max:255',
        ]);

        $warehouse->update($data);

        return response()->json($warehouse);
    }

    /**
     * Delete a warehouse (and cascade to collections).
     */
    public function destroy(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $warehouse = $tenant->warehouses()->findOrFail($id);
        $warehouse->delete();

        return response()->json(['message' => 'Warehouse deleted']);
    }

    public function userWarehouses(Request $request)
    {
        $user = $request->get('user'); // set by user.token middleware

        $warehouses = $user->tenant->warehouses()->where('status', 'active')->get();
        $result = $warehouses->map(function ($warehouse) {
            return [
                'id' => $warehouse->id,
                'name' => $warehouse->name,
                'location' => $warehouse->location,
                'collections' => $warehouse->collections()->count(),
            ];
        });

        return response()->json([
            'response' => 'success',
            'result' => $result,
        ]);
    }
}