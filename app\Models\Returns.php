<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Returns extends Model
{
    protected $fillable = [
        'ref',
        'order_id',
        'user_id',
        'user_name',
        'return_reason',
        'images',
        'status',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->ref)) {
                $model->ref = 'RET-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the order that was returned
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user who created the return
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get images as array
     */
    public function getImagesArrayAttribute()
    {
        return $this->attributes['images'] ? explode(',', $this->attributes['images']) : [];
    }

    /**
     * Set images from array
     */
    public function setImagesFromArray(array $images)
    {
        $this->images = implode(',', $images);
    }
}
