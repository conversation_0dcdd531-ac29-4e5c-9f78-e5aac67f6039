<?php

namespace App\Services;

use App\Models\AppJournal;
use App\Models\User;

class ActivityLogService
{
    /**
     * Log an activity to the app journal
     *
     * @param User $actor The user performing the action
     * @param string $action The action description
     * @return AppJournal|null
     */
    public function log(User $actor, string $action): ?AppJournal
    {
        try {
            return AppJournal::create([
                'actor_id' => $actor->id,
                'actor_name' => $actor->name,
                'action' => $action,
            ]);
        } catch (\Exception $e) {
            // Log error but don't break the main operation
            \Log::error('Failed to log activity: ' . $e->getMessage(), [
                'actor_id' => $actor->id,
                'action' => $action
            ]);
            
            return null;
        }
    }

    /**
     * Log a status change
     *
     * @param User $actor
     * @param string $entity Entity type (order, collection, etc.)
     * @param mixed $entityId
     * @param string $oldStatus
     * @param string $newStatus
     * @return AppJournal|null
     */
    public function logStatusChange(User $actor, string $entity, $entityId, string $oldStatus, string $newStatus): ?AppJournal
    {
        $action = "{$entity} #{$entityId}: {$oldStatus} -> {$newStatus}";
        return $this->log($actor, $action);
    }

    /**
     * Log a creation action
     *
     * @param User $actor
     * @param string $entity
     * @param mixed $entityId
     * @return AppJournal|null
     */
    public function logCreation(User $actor, string $entity, $entityId): ?AppJournal
    {
        $action = "Created {$entity} #{$entityId}";
        return $this->log($actor, $action);
    }

    /**
     * Log an update action
     *
     * @param User $actor
     * @param string $entity
     * @param mixed $entityId
     * @param array $changes Optional array of changed fields
     * @return AppJournal|null
     */
    public function logUpdate(User $actor, string $entity, $entityId, array $changes = []): ?AppJournal
    {
        $action = "Updated {$entity} #{$entityId}";
        
        if (!empty($changes)) {
            $changesList = [];
            foreach ($changes as $field => $change) {
                if (is_array($change) && isset($change['old'], $change['new'])) {
                    $changesList[] = "{$field}: {$change['old']} -> {$change['new']}";
                } else {
                    $changesList[] = "{$field}: {$change}";
                }
            }
            $action .= " (" . implode(', ', $changesList) . ")";
        }
        
        return $this->log($actor, $action);
    }

    /**
     * Log a deletion action
     *
     * @param User $actor
     * @param string $entity
     * @param mixed $entityId
     * @return AppJournal|null
     */
    public function logDeletion(User $actor, string $entity, $entityId): ?AppJournal
    {
        $action = "Deleted {$entity} #{$entityId}";
        return $this->log($actor, $action);
    }

    /**
     * Log a custom action
     *
     * @param User $actor
     * @param string $entity
     * @param mixed $entityId
     * @param string $customAction
     * @return AppJournal|null
     */
    public function logCustomAction(User $actor, string $entity, $entityId, string $customAction): ?AppJournal
    {
        $action = "{$customAction} {$entity} #{$entityId}";
        return $this->log($actor, $action);
    }

    /**
     * Get activity logs for a specific actor
     *
     * @param User $actor
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActorLogs(User $actor, int $limit = 50)
    {
        return AppJournal::where('actor_id', $actor->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent activity logs
     *
     * @param int $limit
     * @param User|null $tenantUser Filter by tenant if provided
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentLogs(int $limit = 100, User $tenantUser = null)
    {
        $query = AppJournal::with('actor')
            ->orderBy('created_at', 'desc')
            ->limit($limit);

        if ($tenantUser) {
            // Filter by tenant - get logs from users in the same tenant
            $query->whereHas('actor', function ($q) use ($tenantUser) {
                $q->where('tenant_id', $tenantUser->tenant_id);
            });
        }

        return $query->get();
    }

    /**
     * Search activity logs
     *
     * @param string $search
     * @param User|null $tenantUser
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function searchLogs(string $search, User $tenantUser = null, int $limit = 100)
    {
        $query = AppJournal::with('actor')
            ->where('action', 'like', "%{$search}%")
            ->orderBy('created_at', 'desc')
            ->limit($limit);

        if ($tenantUser) {
            $query->whereHas('actor', function ($q) use ($tenantUser) {
                $q->where('tenant_id', $tenantUser->tenant_id);
            });
        }

        return $query->get();
    }

    /**
     * Clean old logs (older than specified days)
     *
     * @param int $days
     * @return int Number of deleted records
     */
    public function cleanOldLogs(int $days = 90): int
    {
        $cutoffDate = now()->subDays($days);
        
        return AppJournal::where('created_at', '<', $cutoffDate)->delete();
    }
}
