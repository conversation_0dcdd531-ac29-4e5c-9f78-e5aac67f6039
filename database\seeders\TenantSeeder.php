<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Tenant;

class TenantSeeder extends Seeder
{
    public function run(): void
    {
        // Wipe existing data (safe for any DB)
        DB::table('tenants')->delete();

        // Insert tenants
        Tenant::insert([
            [
                'name' => 'Client A',
                'logo' => 'logos/client-a.png',
                'domain' => 'client-a.myapp.com',
                'token' => generate_strong_token(70),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Client B',
                'logo' => 'logos/client-b.png',
                'domain' => 'client-b.myapp.com',
                'token' => generate_strong_token(70),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }
}