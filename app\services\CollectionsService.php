<?php

namespace App\Services;

use App\Models\Collection;
use App\Models\Warehouse;
use Illuminate\Support\Facades\Validator;

class CollectionsService
{
    public function __construct()
    {
    }

    /**
     * Get collection by id
     */
    public function getCollectionsById($tenant, $user,$id)
    {
        $collection = Collection::withCount('orders')
        ->whereHas('warehouse', function ($q) use ($tenant, $user) {
            if ($tenant) {
                $q->where('tenant_id', $tenant->id);
            }
            if ($user) {
                $q->where('tenant_id', $user->tenant->id);
                $q->where('status', 'active');
            }
        })
        ->find($id);
        if (!$collection) {
            return [
                'response' => 'error',
                'message' => 'Collection not found or unauthorized'
            ];
        }

        return [
            'response' => 'success',
            'result' => $collection,
        ];
    }
}