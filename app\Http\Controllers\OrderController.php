<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Collection;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Validator;
use App\Services\ActivityLogService;

class OrderController extends Controller
{
    protected $activityLogService;

    public function __construct(ActivityLogService $activityLogService)
    {
        $this->activityLogService = $activityLogService;
    }
    /**
     * List all orders for a specific collection (tenant-scoped).
     */
    public function index(Request $request, $collectionId)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        // Make sure the collection belongs to the tenant
        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant, $user) {
            if ($tenant) {
                $q->where('tenant_id', $tenant->id);
            }
            if ($user) {
                $q->where('tenant_id', $user->tenant->id);
                $q->where('status', 'active');
            }
        })->find($collectionId);

        if (!$collection) {
            return response()->json([
                'response' => 'error',
                'message' => 'Collection not found or unauthorized'], 404);
        }


        $orders = $collection->orders()->with('products')->get();

        if ($user) {
            $orders = $orders->map(function ($order) {
                return [
                    'id' => $order->id,
                    'orderCode' => $order->orderCode,
                    'orderNum' => $order->orderNum,
                    'goodsDescription' => $order->goodsDescription,
                    'status' => $order->status,

                    'ordersProducts' => $order->products->map(function ($product) {
                        return [
                            'id' =>  (int)  $product->id,
                            'name' => $product->name,
                            'sku'=> $product->sku,
                            'status' => $product->status,
                            'quantity' => (int) $product->quantity,
                        ];
                    }),

                ];
            });
        }

        return response()->json($orders);
    }

    /**
     * Create a new order under a collection.
     */
    public function store(Request $request, $collectionId)
    {
        $tenant = $request->get('tenant');

        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($collectionId);
        if (!$collection) {
            return response()->json([
                'response' => 'error',
                'message' => 'Collection not found or unauthorized'], 404);
        }

        $validator = Validator::make($request->all(), [
            'orderCode' => 'required|unique:orders,orderCode|string|max:255',
            'orderNum' => 'required|unique:orders,orderNum|string|max:255',
            'trackingNumber' => 'nullable|string|max:255',
            'shippingCompany' => 'nullable|string|max:255',
            'store' => 'nullable|string|max:255',
            'sku' => 'nullable|string|max:255',
            'goodsDescription' => 'nullable|string',
            'goodsValue' => 'nullable|numeric',
            'productLink' => 'nullable|url',
            'currency' => 'nullable|string|max:10',
            'paymentMethod' => 'nullable|string|max:255',
            'appliedOffer' => 'nullable|string|max:255',
            'isUpsell' => 'nullable|boolean',
            'status' => 'required|string|max:255',
            'statusReason' => 'nullable|string',
            'isConfirmed' => 'nullable|boolean',
            'moreInfo' => 'nullable|string',
            'unconfirmedReason' => 'nullable|string',
            'unconfirmedDescription' => 'nullable|string',
            'description' => 'nullable|string',
            'validatedVia' => 'nullable|string|max:255',
            'isexpired' => 'nullable|boolean',
            'followupStatus' => 'nullable|string|max:255',
            'followupRejectReason' => 'nullable|string',
            'followupComment' => 'nullable|string',
            'originCountry' => 'nullable|string|max:255',
            'destinationCountry' => 'nullable|string|max:255',
            'contact' => 'nullable|string|max:255',
            'mobileNumber' => 'nullable|string|max:255',
            'whatsappNumber' => 'nullable|string|max:255',
            'phoneNumber' => 'nullable|string|max:255',
            'language' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'cityId' => 'nullable|integer',
            'city' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',
            'shortAddress' => 'nullable|string|max:255',
            'houseNumber' => 'nullable|string|max:255',
            'nearestPlace' => 'nullable|string|max:255',
            'street' => 'nullable|string|max:255',
            'area' => 'nullable|string|max:255',
            'zipcode' => 'nullable|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        $order = $collection->orders()->create($data);

        return response()->json($order, 201);
    }

    /**
     * Show details of a single order.
     */
    public function show(Request $request, $id)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        $order = Order::with('products')
            ->whereHas('collection.warehouse', function ($q) use ($tenant, $user) {
                if ($tenant) {
                    $q->where('tenant_id', $tenant->id);
                }
                if ($user) {
                    $q->where('tenant_id', $user->tenant->id);
                    $q->where('status', 'active');
                }
            })
            ->find($id);

        if (!$order) {
            return response()->json([
                'response' => 'error',
                'message' => 'Order not found or unauthorized'], 404);
        }

        if ($user) {
            $order = [
                'id' => $order->id,
                'orderCode' => $order->orderCode,
                'orderNum' => $order->orderNum,
                'trackingNumber' => $order->trackingNumber,
                'status' => $order->status,
                'contact' => $order->contact,
                'products_count' => $order->products()->count(),
            ];
        }

        return response()->json($order, 200);
    }

    /**
     * Update an order.
     */
    public function update(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $order = Order::whereHas('collection.warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })
            ->find($id);
            if (!$order) {
            return response()->json([
                'response' => 'error',
                'message' => 'Order not found or unauthorized'], 404);
        }

        $validator = Validator::make($request->all(), [
            'orderCode' => 'sometimes|unique:orders,orderCode,' . $id . '|string|max:255',
            'orderNum' => 'sometimes|unique:orders,orderNum,' . $id . '|string|max:255',
            'trackingNumber' => 'nullable|string|max:255',
            'shippingCompany' => 'nullable|string|max:255',
            'store' => 'nullable|string|max:255',
            'sku' => 'nullable|string|max:255',
            'goodsDescription' => 'nullable|string',
            'goodsValue' => 'nullable|numeric',
            'productLink' => 'nullable|url',
            'currency' => 'nullable|string|max:10',
            'paymentMethod' => 'nullable|string|max:255',
            'appliedOffer' => 'nullable|string|max:255',
            'isUpsell' => 'nullable|boolean',
            'status' => 'sometimes|string|max:255',
            'statusReason' => 'nullable|string',
            'isConfirmed' => 'nullable|boolean',
            'moreInfo' => 'nullable|string',
            'unconfirmedReason' => 'nullable|string',
            'unconfirmedDescription' => 'nullable|string',
            'description' => 'nullable|string',
            'validatedVia' => 'nullable|string|max:255',
            'isexpired' => 'nullable|boolean',
            'followupStatus' => 'nullable|string|max:255',
            'followupRejectReason' => 'nullable|string',
            'followupComment' => 'nullable|string',
            'originCountry' => 'nullable|string|max:255',
            'destinationCountry' => 'nullable|string|max:255',
            'contact' => 'nullable|string|max:255',
            'mobileNumber' => 'nullable|string|max:255',
            'whatsappNumber' => 'nullable|string|max:255',
            'phoneNumber' => 'nullable|string|max:255',
            'language' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'cityId' => 'nullable|integer',
            'city' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',
            'shortAddress' => 'nullable|string|max:255',
            'houseNumber' => 'nullable|string|max:255',
            'nearestPlace' => 'nullable|string|max:255',
            'street' => 'nullable|string|max:255',
            'area' => 'nullable|string|max:255',
            'zipcode' => 'nullable|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        $order->update($data);

        return response()->json($order);
    }

    /**
     * Delete an order.
     */
    public function destroy(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $order = Order::whereHas('collection.warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($id);

        if (!$order) {
            return response()->json([
                'response' => 'error',
                'message' => 'Order not found or unauthorized'], 404);
        }

        $order->delete();

        return response()->json(['message' => 'Order deleted successfully']);
    }

    //toggle orderStatus
    public function setShipped(Request $request, $id)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        $order = Order::whereHas('collection.warehouse', function ($q) use ($tenant, $user) {
            if ($tenant) {
                $q->where('tenant_id', $tenant->id);
            }
            if ($user) {
                $q->where('tenant_id', $user->tenant->id);
                $q->where('status', 'active');
            }
        })->find($id);

        if (!$order) {
            return response()->json([
                'response' => 'error',
                'message' => 'Order not found or unauthorized'], 404);
        }

        $oldStatus = $order->status;
        $order->status = 'shipped';
        $order->save();

        // Log activity if user is authenticated
        if ($user) {
            $this->activityLogService->logStatusChange($user, 'order', $order->id, $oldStatus, 'shipped');
        }

        return response()->json(['response' => 'success', 'result' => $order->status]);
    }
}
