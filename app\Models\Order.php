<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'collection_id',

        // Logical fields from sellers_colis
        'orderCode',
        'orderNum',
        'trackingNumber',
        'shippingCompany',
        'store',
        'sku',
        'goodsDescription',
        'goodsValue',
        'productLink',
        'currency',
        'paymentMethod',
        'appliedOffer',
        'isUpsell',
        'status',
        'statusReason',
        'statusUpdatedAt',
        'isConfirmed',
        'moreInfo',
        'unconfirmedReason',
        'unconfirmedDescription',
        'description',
        'validatedVia',
        'isexpired',
        'followupStatus',
        'followupCreated',
        'followupSchedule',
        'followupRejectReason',
        'followupComment',
        'followupUpdatedAt',
        'originCountry',
        'destinationCountry',
        'shippedAt',
        'returnedAt',
        'deliveredAt',
        'remittanceInvoice',
        'shippingInvoice',
        'callcenterInvoice',
        'contact',
        'mobileNumber',
        'whatsappNumber',
        'phoneNumber',
        'language',
        'country',
        'address',
        'cityId',
        'city',
        'province',
        'shortAddress',
        'houseNumber',
        'nearestPlace',
        'street',
        'area',
        'zipcode',
        'latitude',
        'longitude',
    ];

    protected $casts = [
        'statusUpdatedAt' => 'datetime',
        'followupCreated' => 'datetime',
        'followupUpdatedAt' => 'datetime',
        'shippedAt' => 'datetime',
        'returnedAt' => 'datetime',
        'deliveredAt' => 'datetime',
    ];

    public function collection()
    {
        return $this->belongsTo(Collection::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

}