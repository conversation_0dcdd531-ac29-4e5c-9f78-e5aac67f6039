<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CollectionController;

// Mixed access routes (tenant + JWT users)
Route::middleware('auth')->group(function () {
    Route::get('/collections/{id}', [CollectionController::class, 'show']);
    Route::get('/warehouse/{warehouseId}/collections', [CollectionController::class, 'index']);
    Route::put('/collections/{id}/status', [CollectionController::class, 'updateStatus']);
});

// Tenant-only routes
Route::middleware('tenant.token')->group(function () {
    Route::prefix('collections')->group(function () {
        Route::post('/{warehouseId}', [CollectionController::class, 'store']);
        Route::put('/{id}', [CollectionController::class, 'update']);
        Route::delete('/{id}', [CollectionController::class, 'destroy']);
    });
});
