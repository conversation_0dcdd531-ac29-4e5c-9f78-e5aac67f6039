<?php

namespace App\Http\Middleware;


use Closure;
use Illuminate\Http\Request;
use App\Models\Tenant;

class TenantTokenMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $token = $request->header('X-Tenant-Token');

        if (!$token || !($tenant = Tenant::where('token', $token)->first())) {
            return response()->json(['error' => 'Unauthorized Tenant'], 401);
        }

        $request->merge(['tenant' => $tenant]);
        return $next($request);
    }
}