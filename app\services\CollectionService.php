<?php

namespace App\Services;

use App\Models\Collection;
use App\Models\Warehouse;
use App\Models\Tenant;
use App\Models\User;

class CollectionService
{
    protected $tenantService;

    public function __construct(TenantService $tenantService)
    {
        $this->tenantService = $tenantService;
    }

    /**
     * Get all collections for a warehouse
     */
    public function getCollections($request, int $warehouseId): array
    {
        $tenant = $this->tenantService->getTenantFromRequest($request);
        $user = $this->tenantService->getUserFromRequest($request);

        if (!$tenant) {
            return [
                'success' => false,
                'message' => 'Unauthorized',
                'status' => 401
            ];
        }

        // Check if warehouse belongs to tenant
        $warehouse = $user ? 
            $user->tenant->warehouses()->where('status', 'active')->find($warehouseId) :
            $tenant->warehouses()->find($warehouseId);

        if (!$warehouse) {
            return [
                'success' => false,
                'message' => 'Warehouse not found or unauthorized',
                'status' => 404
            ];
        }

        $collections = $warehouse->collections()->withCount('orders')->get();

        // Format response for users
        if ($user) {
            $collections = collect($this->tenantService->formatCollectionForUser($collections, 'collection'));
        }

        return [
            'success' => true,
            'data' => $collections
        ];
    }

    /**
     * Get a single collection
     */
    public function getCollection($request, int $collectionId): array
    {
        $tenant = $this->tenantService->getTenantFromRequest($request);
        $user = $this->tenantService->getUserFromRequest($request);

        if (!$tenant) {
            return [
                'success' => false,
                'message' => 'Unauthorized',
                'status' => 401
            ];
        }

        $query = Collection::withCount('orders')
            ->whereHas('warehouse', function ($q) use ($tenant, $user) {
                $q->where('tenant_id', $tenant->id);
                if ($user) {
                    $q->where('status', 'active');
                }
            });

        $collection = $query->find($collectionId);

        if (!$collection) {
            return [
                'success' => false,
                'message' => 'Collection not found or unauthorized',
                'status' => 404
            ];
        }

        // Format response for users
        if ($user) {
            $collection = $this->tenantService->formatForUser($collection, 'collection');
        }

        return [
            'success' => true,
            'data' => $collection
        ];
    }

    /**
     * Create a new collection
     */
    public function createCollection(array $data, int $warehouseId, Tenant $tenant): array
    {
        $warehouse = $tenant->warehouses()->find($warehouseId);

        if (!$warehouse) {
            return [
                'success' => false,
                'message' => 'Warehouse not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $collection = $warehouse->collections()->create($data);

            return [
                'success' => true,
                'data' => $collection,
                'message' => 'Collection created successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create collection: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Update a collection
     */
    public function updateCollection(int $collectionId, array $data, Tenant $tenant): array
    {
        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($collectionId);

        if (!$collection) {
            return [
                'success' => false,
                'message' => 'Collection not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $collection->update($data);

            return [
                'success' => true,
                'data' => $collection,
                'message' => 'Collection updated successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update collection: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Delete a collection
     */
    public function deleteCollection(int $collectionId, Tenant $tenant): array
    {
        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($collectionId);

        if (!$collection) {
            return [
                'success' => false,
                'message' => 'Collection not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $collection->delete();

            return [
                'success' => true,
                'message' => 'Collection deleted successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to delete collection: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }
}
