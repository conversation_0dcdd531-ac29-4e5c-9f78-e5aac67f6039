<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\User;

class UserTokenMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $authorizationHeader = $request->header('Authorization');

        if (!$authorizationHeader || !str_starts_with($authorizationHeader, 'Bearer ')) {
            return response()->json(['error' => 'Missing or malformed Bearer token'], 401);
        }

        $token = trim(substr($authorizationHeader, 7)); // Extract the token after "Bearer "

        $user = User::where('api_token', $token)->first();


        if (!$user) {
            return response()->json(['error' => 'Invalid or expired token'], 401);
        }

        $request->merge(['user' => $user]); // attach to request

        return $next($request);
    }
}