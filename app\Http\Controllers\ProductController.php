<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Order;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Validator;

class ProductController extends Controller
{
    /**
     * List all products for a specific order (tenant-scoped).
     */
    public function index(Request $request, $orderId)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        // Make sure the order belongs to the tenant
        $order = Order::whereHas('collection.warehouse', function ($q) use ($tenant, $user) {
            if ($tenant) {
                $q->where('tenant_id', $tenant->id);
            }
            if ($user) {
                $q->where('tenant_id', $user->tenant->id);
                $q->where('status', 'active');
            }
        })->find($orderId);
        if (!$order) {
            return response()->json([
                'response' => 'error',
                'message' => 'Order not found or unauthorized'], 404);
        }

        $products = $order->products()->get();

        if ($user) {
            $products = $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'status' => $product->status,
                    'quantity' => $product->quantity,
                    'weight' => $product->weight,
                ];
            });
        }

        return response()->json($products);
    }

    /**
     * Create a new product under an order.
     */
    public function store(Request $request, $orderId)
    {
        $tenant = $request->get('tenant');

        $order = Order::whereHas('collection.warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($orderId);
        if (!$order) {
            return response()->json([
                'response' => 'error',
                'message' => 'Order not found or unauthorized'], 404);
        }

        $validator = Validator::make($request->all(), [
            'sellerId' => 'nullable|integer',
            'sellerName' => 'nullable|string|max:255',
            'name' => 'nullable|string|max:255',
            'arabicName' => 'nullable|string|max:255',
            'sku' => 'required|string|max:255|unique:products,sku',
            'weight' => 'nullable|numeric',
            'width' => 'nullable|numeric',
            'height' => 'nullable|numeric',
            'length' => 'nullable|numeric',
            'status' => 'nullable|string|max:255',
            'isArchive' => 'nullable|boolean',
            'descriptionCallcenter' => 'nullable|string',
            'productLink' => 'nullable|url',
            'productVideo' => 'nullable|url',
            'warehouseId' => 'nullable|integer',
            'warehouseName' => 'nullable|string|max:255',
            'shippingPriceType' => 'nullable|string|max:255',
            'shippingBy' => 'nullable|string|max:255',
            'confirmedBy' => 'nullable|string|max:255',
            'createdBy' => 'nullable|string|max:255',
            'updatedBy' => 'nullable|string|max:255',
            'listeStock' => 'nullable|json',
            'productType' => 'nullable|string|max:255',
            'shippingType' => 'nullable|string|max:255',
            'quantity' => 'nullable|integer',
            'parent' => 'nullable|string|max:255',
            'hscode' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'returnImages' => 'nullable|string',
            'returnReason' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['status'] = 'pending';
        $product = $order->products()->create($data);

        return response()->json($product, 201);
    }

    /**
     * Toggle status to gathred by user or back to pending
     */
    public function gather(Request $request, $id)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');


        $product = Product::whereHas('order.collection.warehouse', function ($q) use ($tenant, $user) {
            if ($tenant) {
                $q->where('tenant_id', $tenant->id);
            }
            if ($user) {
                $q->where('tenant_id', $user->tenant->id);
                $q->where('status', 'active');
            }
        })->find($id);

        if (!$product) {
            return response()->json([
                'response' => 'error',
                'message' => 'Product not found or unauthorized'], 404);
        }

        if ($product->status == 'gathered') {
            $product->status = 'pending';
        } else {
            $product->status = 'gathered';
        }

        $product->save();

        return response()->json(['response' => 'success', 'result' => $product->status]);
        }



    //get orders by order's sku (not product sku) and toggle all they products status
    public function gatherBySku(Request $request, $sku)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        $products = Product::whereHas('order', function ($q) use ($sku, $tenant, $user) {
            $q->where('sku', $sku);

            $q->whereHas('collection.warehouse', function ($q2) use ($tenant, $user) {
                if ($tenant) {
                    $q2->where('tenant_id', $tenant->id);
                }
                if ($user) {
                    $q2->where('tenant_id', $user->tenant->id);
                    $q2->where('status', 'active');
                }
            });
        })->get();

        if ($products->isEmpty()) {
            return response()->json([
                'response' => 'error',
                'message' => 'No matching products found or unauthorized'
            ], 404);
        }

        foreach ($products as $product) {
            $product->status = $product->status === 'gathered' ? 'pending' : 'gathered';
            $product->save();
        }

        return response()->json([
            'response' => 'success',
            'message' => 'Products updated successfully',
            'data' => $products
        ]);
    }




    /**
     * Show details of a single product.
     */
    public function show(Request $request, $id)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        $product = Product::whereHas('order.collection.warehouse', function ($q) use ($tenant, $user) {
            if ($tenant) {
                $q->where('tenant_id', $tenant->id);
            }
            if ($user) {
                $q->where('tenant_id', $user->tenant->id);
                $q->where('status', 'active');
            }
        })->find($id);

        if (!$product) {
            return response()->json([
                'response' => 'error',
                'message' => 'Product not found or unauthorized'], 404);
        }

        if ($user) {
            $product = [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'status' => $product->status,
                'quantity' => $product->quantity,
                'weight' => $product->weight,
            ];
        }

        return response()->json($product, 200);
    }

    /**
     * Update a product.
     */
    public function update(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $product = Product::whereHas('order.collection.warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($id);

        if (!$product) {
            return response()->json([
                'response' => 'error',
                'message' => 'Product not found or unauthorized'], 404);
        }

        $validator = Validator::make($request->all(), [
            'sellerId' => 'nullable|integer',
            'sellerName' => 'nullable|string|max:255',
            'name' => 'nullable|string|max:255',
            'arabicName' => 'nullable|string|max:255',
            'sku' => 'sometimes|string|max:255|unique:products,sku,' . $id,
            'weight' => 'nullable|numeric',
            'width' => 'nullable|numeric',
            'height' => 'nullable|numeric',
            'length' => 'nullable|numeric',
            'status' => 'nullable|string|max:255',
            'isArchive' => 'nullable|boolean',
            'descriptionCallcenter' => 'nullable|string',
            'productLink' => 'nullable|url',
            'productVideo' => 'nullable|url',
            'warehouseId' => 'nullable|integer',
            'warehouseName' => 'nullable|string|max:255',
            'shippingPriceType' => 'nullable|string|max:255',
            'shippingBy' => 'nullable|string|max:255',
            'confirmedBy' => 'nullable|string|max:255',
            'createdBy' => 'nullable|string|max:255',
            'updatedBy' => 'nullable|string|max:255',
            'listeStock' => 'nullable|json',
            'productType' => 'nullable|string|max:255',
            'shippingType' => 'nullable|string|max:255',
            'quantity' => 'nullable|integer',
            'parent' => 'nullable|string|max:255',
            'hscode' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'returnImages' => 'nullable|string',
            'returnReason' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        $product->update($data);

        return response()->json($product);
    }

    /**
     * Delete a product.
     */
    public function destroy(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $product = Product::whereHas('order.collection.warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($id);

        if (!$product) {
            return response()->json([
                'response' => 'error',
                'message' => 'Product not found or unauthorized'], 404);
        }

        $product->delete();

        return response()->json(['message' => 'Product deleted successfully']);
    }
}