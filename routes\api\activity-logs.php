<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ActivityLogController;

// JWT authenticated user routes only
Route::middleware('jwt.auth')->group(function () {
    Route::prefix('activity-logs')->group(function () {
        Route::get('/', [ActivityLogController::class, 'index']);
        Route::get('/my-logs', [ActivityLogController::class, 'myLogs']);
        Route::get('/search', [ActivityLogController::class, 'search']);
    });
});
