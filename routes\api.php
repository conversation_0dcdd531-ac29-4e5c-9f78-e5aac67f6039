<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('v1')->group(function () {

    // Authentication routes
    require __DIR__ . '/api/auth.php';

    // Warehouse routes
    require __DIR__ . '/api/warehouses.php';

    // Collection routes
    require __DIR__ . '/api/collections.php';

    // Order routes
    require __DIR__ . '/api/orders.php';

    // Product routes
    require __DIR__ . '/api/products.php';

    // Dashboard routes
    require __DIR__ . '/api/dashboard.php';

});