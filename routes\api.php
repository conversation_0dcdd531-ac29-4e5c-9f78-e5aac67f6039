<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProductController;

Route::prefix('v1')->group(function () {
// Public routes — NO token required
Route::post('/login', [AuthController::class, 'login']);
Route::post('tenants/register', [AuthController::class, 'registerTenant']); //todelete after

Route::middleware('auth')->group(function () {
    //collections
    Route::get('/collections/{id}', [CollectionController::class, 'show']);
    Route::get('/warehouse/{warehouseId}/collections', [CollectionController::class, 'index']);
    //warehouses
    Route::get('/warehouses', [WarehouseController::class, 'index']);
    //orders
    Route::get('/orders/{id}', [OrderController::class, 'show']);
    Route::get('/collection/{collectionId}/orders', [OrderController::class, 'index']);
    //products
    Route::get('/products/{id}', [ProductController::class, 'show']);
    Route::get('/order/{orderId}/products', [ProductController::class, 'index']);
    Route::put('/products/{id}/gather', [ProductController::class, 'gather']);

});

// Routes accessible to tenants (registration/login)
Route::middleware('tenant.token')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);

    Route::put('/{sku}', [ProductController::class, 'gatherBySku']);


    // Collection routes with prefix
    Route::prefix('collections')->group(function () {
        Route::post('/{warehouseId}', [CollectionController::class, 'store']);
        Route::put('/{id}', [CollectionController::class, 'update']);
        Route::delete('/{id}', [CollectionController::class, 'destroy']);
    });

    // Warehouse routes with prefix
    Route::prefix('warehouse')->group(function () {
        Route::post('/', [WarehouseController::class, 'store']);
        Route::put('/{warehouseId}', [WarehouseController::class, 'update']);
        Route::delete('/{warehouseId}', [WarehouseController::class, 'destroy']);
    });

    // Order routes with prefix
    Route::prefix('orders')->group(function () {
        Route::post('/{collectionId}', [OrderController::class, 'store']);
        Route::put('/{id}', [OrderController::class, 'update']);
        Route::delete('/{id}', [OrderController::class, 'destroy']);
    });

    // Product routes with prefix
    Route::prefix('products')->group(function () {
        Route::post('/{orderId}', [ProductController::class, 'store']);
        Route::put('/{id}', [ProductController::class, 'update']);
        Route::delete('/{id}', [ProductController::class, 'destroy']);
    });

});

// Routes accessible to authenticated users (via Bearer token)
Route::prefix('user')->middleware('user.token')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/profile', function (Request $request) {
        return response()->json([
            'user' => $request->get('user'),
        ]);
    });
});

});