<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Collection;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Database\QueryException;

class OrderService
{
    protected $tenantService;

    public function __construct(TenantService $tenantService)
    {
        $this->tenantService = $tenantService;
    }

    /**
     * Get all orders for a collection
     */
    public function getOrders($request, int $collectionId): array
    {
        $tenant = $this->tenantService->getTenantFromRequest($request);
        $user = $this->tenantService->getUserFromRequest($request);

        if (!$tenant) {
            return [
                'success' => false,
                'message' => 'Unauthorized',
                'status' => 401
            ];
        }

        // Check if collection belongs to tenant
        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant, $user) {
            $q->where('tenant_id', $tenant->id);
            if ($user) {
                $q->where('status', 'active');
            }
        })->find($collectionId);

        if (!$collection) {
            return [
                'success' => false,
                'message' => 'Collection not found or unauthorized',
                'status' => 404
            ];
        }

        $orders = $collection->orders()->with('products')->get();

        // Format response for users
        if ($user) {
            $orders = collect($this->tenantService->formatCollectionForUser($orders, 'order'));
        }

        return [
            'success' => true,
            'data' => $orders
        ];
    }

    /**
     * Get a single order
     */
    public function getOrder($request, int $orderId): array
    {
        $tenant = $this->tenantService->getTenantFromRequest($request);
        $user = $this->tenantService->getUserFromRequest($request);

        if (!$tenant) {
            return [
                'success' => false,
                'message' => 'Unauthorized',
                'status' => 401
            ];
        }

        $order = Order::with('products')
            ->whereHas('collection.warehouse', function ($q) use ($tenant, $user) {
                $q->where('tenant_id', $tenant->id);
                if ($user) {
                    $q->where('status', 'active');
                }
            })
            ->find($orderId);

        if (!$order) {
            return [
                'success' => false,
                'message' => 'Order not found or unauthorized',
                'status' => 404
            ];
        }

        // Format response for users
        if ($user) {
            $order = $this->tenantService->formatForUser($order, 'order');
        }

        return [
            'success' => true,
            'data' => $order
        ];
    }

    /**
     * Create a new order
     */
    public function createOrder(array $data, int $collectionId, Tenant $tenant): array
    {
        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($collectionId);

        if (!$collection) {
            return [
                'success' => false,
                'message' => 'Collection not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $order = $collection->orders()->create($data);

            return [
                'success' => true,
                'data' => $order,
                'message' => 'Order created successfully'
            ];
        } catch (QueryException $e) {
            // Handle unique constraint violations
            if ($e->errorInfo[1] == 1062) {
                return [
                    'success' => false,
                    'message' => 'Order code or order number already exists',
                    'errors' => [
                        'orderCode' => ['The order code has already been taken.'],
                        'orderNum' => ['The order number has already been taken.']
                    ],
                    'status' => 422
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to create order: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Update an order
     */
    public function updateOrder(int $orderId, array $data, Tenant $tenant): array
    {
        $order = Order::whereHas('collection.warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($orderId);

        if (!$order) {
            return [
                'success' => false,
                'message' => 'Order not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $order->update($data);

            return [
                'success' => true,
                'data' => $order,
                'message' => 'Order updated successfully'
            ];
        } catch (QueryException $e) {
            // Handle unique constraint violations
            if ($e->errorInfo[1] == 1062) {
                return [
                    'success' => false,
                    'message' => 'Order code or order number already exists',
                    'errors' => [
                        'orderCode' => ['The order code has already been taken.'],
                        'orderNum' => ['The order number has already been taken.']
                    ],
                    'status' => 422
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to update order: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Delete an order
     */
    public function deleteOrder(int $orderId, Tenant $tenant): array
    {
        $order = Order::whereHas('collection.warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($orderId);

        if (!$order) {
            return [
                'success' => false,
                'message' => 'Order not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $order->delete();

            return [
                'success' => true,
                'message' => 'Order deleted successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to delete order: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }
}
