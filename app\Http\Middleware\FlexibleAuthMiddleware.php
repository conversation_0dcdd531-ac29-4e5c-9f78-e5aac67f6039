<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Tenant;
use App\Models\User;

class FlexibleAuthMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Try Tenant token first
        $tenantToken = $request->header('X-Tenant-Token');

        if ($tenantToken) {
            $tenant = Tenant::where('token', $tenantToken)->first();

            if (!$tenant) {
                return response()->json(['error' => 'Invalid tenant token'], 401);
            }

            $request->merge(['tenant' => $tenant]);
            return $next($request);
        }

        // Try User token (Bearer)
        $authHeader = $request->header('Authorization');

        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            $userToken = trim(substr($authHeader, 7));
            $user = User::where('api_token', $userToken)->first();

            if (!$user) {
                return response()->json(['error' => 'Invalid user token'], 401);
            }

            $request->merge(['user' => $user]);
            return $next($request);
        }

        return response()->json(['error' => 'Unauthorized'], 401);
    }
}