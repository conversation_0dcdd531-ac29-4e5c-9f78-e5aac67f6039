<?php

namespace App\Services;

use App\Models\Returns;
use App\Models\Order;
use App\Models\User;

class ReturnsService
{
    protected $fileUploadService;
    protected $activityLogService;
    protected $tenantService;

    public function __construct(
        FileUploadService $fileUploadService,
        ActivityLogService $activityLogService,
        TenantService $tenantService
    ) {
        $this->fileUploadService = $fileUploadService;
        $this->activityLogService = $activityLogService;
        $this->tenantService = $tenantService;
    }

    /**
     * Create a new return
     */
    public function createReturn(array $data, array $images, User $user): array
    {
        try {
            // Validate order exists and belongs to user's tenant
            $order = Order::whereHas('collection.warehouse', function ($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id);
            })->find($data['order_id']);

            if (!$order) {
                return [
                    'success' => false,
                    'message' => 'Order not found or unauthorized',
                    'status' => 404
                ];
            }

            // Check if return already exists for this order
            $existingReturn = Returns::where('order_id', $data['order_id'])->first();
            if ($existingReturn) {
                return [
                    'success' => false,
                    'message' => 'Return already exists for this order',
                    'status' => 422
                ];
            }

            // Upload images if provided
            $imageUrls = '';
            if (!empty($images)) {
                $uploadResult = $this->fileUploadService->uploadMultipleFiles($images, 'upload/returns');
                
                if (!$uploadResult['success']) {
                    return [
                        'success' => false,
                        'message' => 'Failed to upload images',
                        'errors' => $uploadResult['errors'],
                        'status' => 422
                    ];
                }
                
                $imageUrls = $uploadResult['urls_string'];
            }

            // Create return
            $return = Returns::create([
                'order_id' => $data['order_id'],
                'user_id' => $user->id,
                'user_name' => $user->name,
                'return_reason' => $data['return_reason'],
                'images' => $imageUrls,
                'status' => 'pending',
            ]);

            // Log activity
            $this->activityLogService->logCreation($user, 'return', $return->id);

            return [
                'success' => true,
                'data' => $return,
                'message' => 'Return created successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create return: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Get returns for user's tenant
     */
    public function getReturns($request, array $filters = []): array
    {
        $user = $this->tenantService->getUserFromRequest($request);
        $tenant = $this->tenantService->getTenantFromRequest($request);

        if (!$tenant) {
            return [
                'success' => false,
                'message' => 'Unauthorized',
                'status' => 401
            ];
        }

        $query = Returns::with(['order', 'user'])
            ->whereHas('order.collection.warehouse', function ($q) use ($tenant) {
                $q->where('tenant_id', $tenant->id);
            });

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['order_id'])) {
            $query->where('order_id', $filters['order_id']);
        }

        $returns = $query->orderBy('created_at', 'desc')->get();

        // Format response for users
        if ($user) {
            $returns = $returns->map(function ($return) {
                return [
                    'id' => $return->id,
                    'ref' => $return->ref,
                    'order_id' => $return->order_id,
                    'return_reason' => $return->return_reason,
                    'status' => $return->status,
                    'created_at' => $return->created_at,
                    'images_count' => count($return->images_array),
                ];
            });
        }

        return [
            'success' => true,
            'data' => $returns
        ];
    }

    /**
     * Get a single return
     */
    public function getReturn($request, int $returnId): array
    {
        $user = $this->tenantService->getUserFromRequest($request);
        $tenant = $this->tenantService->getTenantFromRequest($request);

        if (!$tenant) {
            return [
                'success' => false,
                'message' => 'Unauthorized',
                'status' => 401
            ];
        }

        $return = Returns::with(['order', 'user'])
            ->whereHas('order.collection.warehouse', function ($q) use ($tenant) {
                $q->where('tenant_id', $tenant->id);
            })
            ->find($returnId);

        if (!$return) {
            return [
                'success' => false,
                'message' => 'Return not found or unauthorized',
                'status' => 404
            ];
        }

        // Format response for users
        if ($user) {
            $return = [
                'id' => $return->id,
                'ref' => $return->ref,
                'order_id' => $return->order_id,
                'return_reason' => $return->return_reason,
                'status' => $return->status,
                'created_at' => $return->created_at,
                'images' => $return->images_array,
            ];
        }

        return [
            'success' => true,
            'data' => $return
        ];
    }

    /**
     * Update return status
     */
    public function updateReturnStatus(int $returnId, string $newStatus, User $user): array
    {
        $return = Returns::whereHas('order.collection.warehouse', function ($q) use ($user) {
            $q->where('tenant_id', $user->tenant_id);
        })->find($returnId);

        if (!$return) {
            return [
                'success' => false,
                'message' => 'Return not found or unauthorized',
                'status' => 404
            ];
        }

        $oldStatus = $return->status;
        $return->status = $newStatus;
        $return->save();

        // Log activity
        $this->activityLogService->logStatusChange($user, 'return', $return->id, $oldStatus, $newStatus);

        return [
            'success' => true,
            'data' => $return,
            'message' => 'Return status updated successfully'
        ];
    }

    /**
     * Delete a return
     */
    public function deleteReturn(int $returnId, User $user): array
    {
        $return = Returns::whereHas('order.collection.warehouse', function ($q) use ($user) {
            $q->where('tenant_id', $user->tenant_id);
        })->find($returnId);

        if (!$return) {
            return [
                'success' => false,
                'message' => 'Return not found or unauthorized',
                'status' => 404
            ];
        }

        // Delete associated images
        if ($return->images) {
            $this->fileUploadService->deleteFiles($return->images);
        }

        // Log activity before deletion
        $this->activityLogService->logDeletion($user, 'return', $return->id);

        $return->delete();

        return [
            'success' => true,
            'message' => 'Return deleted successfully'
        ];
    }
}
