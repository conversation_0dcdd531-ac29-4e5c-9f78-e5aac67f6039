<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WarehouseController;

// Mixed access routes (tenant + JWT users)
Route::middleware('auth')->group(function () {
    Route::get('/warehouses', [WarehouseController::class, 'index']);
});

// Tenant-only routes
Route::middleware('tenant.token')->group(function () {
    Route::prefix('warehouse')->group(function () {
        Route::post('/', [WarehouseController::class, 'store']);
        Route::put('/{warehouseId}', [WarehouseController::class, 'update']);
        Route::delete('/{warehouseId}', [WarehouseController::class, 'destroy']);
    });
});
