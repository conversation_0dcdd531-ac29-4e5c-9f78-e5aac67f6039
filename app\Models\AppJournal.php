<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class AppJournal extends Model
{
    protected $table = 'app_journal';

    protected $fillable = [
        'ref',
        'actor_id',
        'actor_name',
        'action',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->ref)) {
                $model->ref = 'LOG-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the user who performed the action
     */
    public function actor()
    {
        return $this->belongsTo(User::class, 'actor_id');
    }
}
