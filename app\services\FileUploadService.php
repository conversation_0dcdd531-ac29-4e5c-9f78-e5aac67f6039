<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadService
{
    /**
     * Allowed image extensions
     */
    private const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    /**
     * Maximum file size in bytes (5MB)
     */
    private const MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * Upload multiple files to a specific folder
     *
     * @param array $files Array of UploadedFile instances
     * @param string $folder Target folder (e.g., 'upload/returns')
     * @return array
     */
    public function uploadMultipleFiles(array $files, string $folder): array
    {
        $uploadedFiles = [];
        $errors = [];

        foreach ($files as $index => $file) {
            if (!$file instanceof UploadedFile) {
                $errors[] = "File at index {$index} is not a valid upload file";
                continue;
            }

            $result = $this->uploadSingleFile($file, $folder);
            
            if ($result['success']) {
                $uploadedFiles[] = $result['url'];
            } else {
                $errors[] = "File at index {$index}: " . $result['error'];
            }
        }

        return [
            'success' => empty($errors),
            'uploaded_files' => $uploadedFiles,
            'errors' => $errors,
            'urls_string' => implode(',', $uploadedFiles)
        ];
    }

    /**
     * Upload a single file
     *
     * @param UploadedFile $file
     * @param string $folder
     * @return array
     */
    public function uploadSingleFile(UploadedFile $file, string $folder): array
    {
        try {
            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            // Generate unique filename
            $extension = $file->getClientOriginalExtension();
            $filename = $this->generateUniqueFilename($extension);
            
            // Ensure folder exists
            $this->ensureFolderExists($folder);
            
            // Store file
            $path = $file->storeAs($folder, $filename, 'public');
            
            if (!$path) {
                return [
                    'success' => false,
                    'error' => 'Failed to store file'
                ];
            }

            // Generate full URL
            $url = Storage::url($path);
            $fullUrl = url($url);

            return [
                'success' => true,
                'path' => $path,
                'url' => $fullUrl,
                'filename' => $filename
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Upload failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate uploaded file
     *
     * @param UploadedFile $file
     * @return array
     */
    private function validateFile(UploadedFile $file): array
    {
        // Check if file is valid
        if (!$file->isValid()) {
            return [
                'valid' => false,
                'error' => 'Invalid file upload'
            ];
        }

        // Check file size
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            return [
                'valid' => false,
                'error' => 'File size exceeds maximum allowed size (5MB)'
            ];
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            return [
                'valid' => false,
                'error' => 'File type not allowed. Allowed types: ' . implode(', ', self::ALLOWED_EXTENSIONS)
            ];
        }

        // Check MIME type
        $mimeType = $file->getMimeType();
        if (!str_starts_with($mimeType, 'image/')) {
            return [
                'valid' => false,
                'error' => 'File must be an image'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Generate unique filename
     *
     * @param string $extension
     * @return string
     */
    private function generateUniqueFilename(string $extension): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return "{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Ensure folder exists
     *
     * @param string $folder
     * @return void
     */
    private function ensureFolderExists(string $folder): void
    {
        if (!Storage::disk('public')->exists($folder)) {
            Storage::disk('public')->makeDirectory($folder);
        }
    }

    /**
     * Delete files by URLs
     *
     * @param string|array $urls
     * @return array
     */
    public function deleteFiles($urls): array
    {
        $urlsArray = is_string($urls) ? explode(',', $urls) : $urls;
        $deleted = [];
        $errors = [];

        foreach ($urlsArray as $url) {
            $url = trim($url);
            if (empty($url)) continue;

            try {
                // Extract path from URL
                $path = $this->extractPathFromUrl($url);
                
                if ($path && Storage::disk('public')->exists($path)) {
                    Storage::disk('public')->delete($path);
                    $deleted[] = $url;
                } else {
                    $errors[] = "File not found: {$url}";
                }
            } catch (\Exception $e) {
                $errors[] = "Failed to delete {$url}: " . $e->getMessage();
            }
        }

        return [
            'success' => empty($errors),
            'deleted' => $deleted,
            'errors' => $errors
        ];
    }

    /**
     * Extract storage path from full URL
     *
     * @param string $url
     * @return string|null
     */
    private function extractPathFromUrl(string $url): ?string
    {
        // Remove base URL and storage prefix
        $baseUrl = url('/storage/');
        
        if (str_starts_with($url, $baseUrl)) {
            return str_replace($baseUrl, '', $url);
        }

        return null;
    }
}
