# API Routes Structure

This directory contains organized API route files for the warehouse management system.

## File Structure

```
routes/api/
├── auth.php          # Authentication routes (login, register, logout, etc.)
├── warehouses.php    # Warehouse management routes
├── collections.php   # Collection management routes
├── orders.php        # Order management routes
├── products.php      # Product management routes
├── dashboard.php     # Dashboard and analytics routes
└── README.md         # This documentation file
```

## Route Organization

### Authentication Routes (`auth.php`)
- **Public Routes**: Login, tenant registration
- **Tenant Routes**: User registration under tenant
- **JWT Routes**: User logout, token refresh, profile

### Warehouse Routes (`warehouses.php`)
- **Mixed Access**: List warehouses (tenant + JWT users)
- **Tenant Only**: Create, update, delete warehouses

### Collection Routes (`collections.php`)
- **Mixed Access**: View collections, update status
- **Tenant Only**: Create, update, delete collections

### Order Routes (`orders.php`)
- **Mixed Access**: View orders, set shipped status
- **Tenant Only**: Create, update, delete orders

### Product Routes (`products.php`)
- **Mixed Access**: View products, gather products
- **Tenant Only**: Create, update, delete products, gather by SKU

### Dashboard Routes (`dashboard.php`)
- **Mixed Access**: Analytics and dashboard data

## Middleware Groups

### `auth` - Mixed Access (Tenant + JWT Users)
- Supports both tenant tokens and JWT tokens
- Used for read operations and some status updates
- Users get filtered/limited data

### `tenant.token` - Tenant Only
- Requires tenant authentication token
- Used for create, update, delete operations
- Full access to all data

### `jwt.auth` - JWT Users Only
- Requires valid JWT token
- Used for user-specific operations
- Scoped to user's tenant

## Usage

All routes are automatically loaded through the main `routes/api.php` file:

```php
Route::prefix('v1')->group(function () {
    require __DIR__ . '/api/auth.php';
    require __DIR__ . '/api/warehouses.php';
    require __DIR__ . '/api/collections.php';
    require __DIR__ . '/api/orders.php';
    require __DIR__ . '/api/products.php';
    require __DIR__ . '/api/dashboard.php';
});
```

## Adding New Routes

1. Create a new file in `routes/api/` directory
2. Add the route definitions with appropriate middleware
3. Include the file in `routes/api.php`
4. Update this README with the new route information
