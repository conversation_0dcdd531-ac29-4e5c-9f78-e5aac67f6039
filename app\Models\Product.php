<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'sellerId',
        'sellerName',
        'name',
        'arabicName',
        'sku',
        'weight',
        'width',
        'height',
        'length',
        'status',
        'isArchive',
        'descriptionCallcenter',
        'productLink',
        'productVideo',
        'warehouseId',
        'warehouseName',
        'shippingPriceType',
        'shippingBy',
        'confirmedBy',
        'confirmedAt',
        'createdBy',
        'updatedBy',
        'listeStock',
        'productType',
        'shippingType',
        'quantity',
        'parent',
        'hscode',
        'category',

        // Additional
        'returnImages',
        'returnReason',
    ];

    protected $casts = [
        'confirmedAt' => 'datetime',
        'isArchive' => 'boolean',
        'quantity' => 'integer',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

}
