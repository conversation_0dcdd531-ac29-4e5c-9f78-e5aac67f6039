<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
{
    Schema::create('products', function (Blueprint $table) {
        $table->id();
        $table->unsignedBigInteger('sellerId')->nullable();
        $table->string('sellerName')->nullable();
        $table->string('name')->nullable();
        $table->string('arabicName')->nullable();
        $table->string('sku')->unique();
        $table->float('weight')->nullable();
        $table->float('width')->nullable();
        $table->float('height')->nullable();
        $table->float('length')->nullable();
        $table->string('status')->nullable();
        $table->boolean('isArchive')->default(false);
        $table->text('descriptionCallcenter')->nullable();
        $table->string('productLink')->nullable();
        $table->string('productVideo')->nullable();
        $table->unsignedBigInteger('warehouseId')->nullable();
        $table->string('warehouseName')->nullable();
        $table->string('shippingPriceType')->nullable();
        $table->string('shippingBy')->nullable();
        $table->string('confirmedBy')->nullable();
        $table->timestamp('confirmedAt')->nullable();
        $table->string('createdBy')->nullable();
        $table->string('updatedBy')->nullable();
        $table->json('listeStock')->nullable();
        $table->string('productType')->nullable();
        $table->string('shippingType')->nullable();
        $table->integer('quantity')->nullable();
        $table->string('parent')->nullable();
        $table->string('hscode')->nullable();
        $table->string('category')->nullable();

        // Custom fields
        $table->json('returnImages')->nullable(); // Assuming multiple image URLs
        $table->text('returnReason')->nullable();

        $table->unsignedBigInteger('order_id')->nullable();
        $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');


        $table->timestamps();
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};