<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Collection extends Model
{
    use HasFactory;

    protected $fillable = [
        'warehouse_id',
        'name',
        'num',
        'added_at',
        'status',
        'shipping_type',
        'shipped_at',
        'shipped_by',
        'orders_count',
    ];

    protected $casts = [
        'added_at' => 'date',
        'shipped_at' => 'datetime',
    ];

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }
}