<?php

namespace App\Services;

use App\Models\User;
use App\Models\Tenant;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Tymon\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Facades\Auth;

class AuthService
{
    /**
     * Register a new tenant
     */
    public function registerTenant(array $data): array
    {
        $tenant = Tenant::create([
            'name' => $data['name'],
            'domain' => $data['domain'],
            'logo' => $data['logo'] ?? null,
            'token' => Str::random(64),
        ]);

        return [
            'response' => 'success',
            'message' => 'Tenant registered successfully',
            'tenant' => $tenant
        ];
    }

    /**
     * Register a new user under a tenant
     */
    public function registerUser(array $data, Tenant $tenant): array
    {
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'tenant_id' => $tenant->id,
        ]);

        return [
            'response' => 'success',
            'message' => 'User registered successfully',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'tenant_id' => $user->tenant_id,
            ]
        ];
    }

    /**
     * Login user and return JWT token
     */
    public function loginUser(array $credentials): array
    {
        // Set the guard to api for JWT authentication
        Auth::shouldUse('api');

        if (!$token = JWTAuth::attempt($credentials)) {
            return [
                'response' => 'error',
                'message' => 'Invalid credentials',
            ];
        }

        $user = Auth::user();

        return [
            'response' => 'success',
            'message' => 'Login successful',

            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'tenant_id' => $user->tenant_id,
            ],
            'token' => $token,
            'token_type' => 'bearer',
            'expires_in' => JWTAuth::factory()->getTTL() * 100000
        ];
    }

    /**
     * Logout user (invalidate token)
     */
    public function logoutUser(): array
    {
        try {
            JWTAuth::invalidate(JWTAuth::getToken());

            return [
                'response' => 'success',
                'message' => 'Successfully logged out'
            ];
        } catch (\Exception $e) {
            return [
                'response' => 'error',
                'message' => 'Failed to logout'
            ];
        }
    }

    /**
     * Refresh JWT token
     */
    public function refreshToken(): array
    {
        try {
            $token = JWTAuth::refresh(JWTAuth::getToken());

            return [
                'response' => 'success',
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => JWTAuth::factory()->getTTL() * 60
            ];
        } catch (\Exception $e) {
            return [
                'response' => 'error',
                'message' => 'Token could not be refreshed'
            ];
        }
    }

    /**
     * Get user profile
     */
    public function getUserProfile(User $user): array
    {
        return [
            'response' => 'success',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'tenant_id' => $user->tenant_id,
                'tenant' => [
                    'id' => $user->tenant->id,
                    'name' => $user->tenant->name,
                    'domain' => $user->tenant->domain,
                ]
            ]
        ];
    }
}