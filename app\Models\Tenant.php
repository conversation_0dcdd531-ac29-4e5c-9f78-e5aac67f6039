<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tenant extends Model
{

    use HasFactory;

    protected $fillable = [
        'name', 'domain', 'logo', 'token',
    ];

    public function users()
    {
        return $this->hasMany(User::class);
    }
    public function warehouses()
    {
        return $this->hasMany(Warehouse::class);
    }
}