<?php
namespace App\Http\Controllers;
use Illuminate\Http\Request;use App\Models\Collection;
use App\Models\Order;
use App\Models\Product;use App\Models\Warehouse;
class DashboardController extends Controller
{
    /**
     * Get all orders shipped by warehouse , apply date range, pagination
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShippedOrders(Request $request, $warehouseId)
    {
        $warehouse = $this->getWarehouse($request, $warehouseId);
        if (!$warehouse) {
            return response()->json([
                'response' => 'error',
                'message' => 'Warehouse not found or unauthorized'], 404);
        }

        $orders = $this->getFilteredOrders($request, $warehouse);

        return response()->json([
            'response' => 'success',
            'result' => $orders->get(),
        ]);
    }

    /**
     * Get warehouse by ID with tenant validation
     * @param Request $request
     * @param int $warehouseId
     * @return Warehouse|null
     */
    private function getWarehouse(Request $request, $warehouseId)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        return Warehouse::whereHas('tenant', function ($q) use ($tenant, $user) {
            if ($tenant) {
                $q->where('id', $tenant->id);
            }
            if ($user) {
                $q->where('id', $user->tenant->id);
            }
        })->find($warehouseId);
    }

    /**
     * Get filtered orders query
     * @param Request $request
     * @param Warehouse $warehouse
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function getFilteredOrders(Request $request, Warehouse $warehouse)
    {
        $orders = Order::whereHas('collection', function ($q) use ($warehouse) {
            $q->where('warehouse_id', $warehouse->id);
        })->where('status', 'shipped');

        if($request->query('start_date') && $request->query('end_date'))
        {
            $orders->where('shippedAt', '>=', $request->query('start_date'))
                ->where('shippedAt', '<=', $request->query('end_date'));
        }
        if($request->query('per_page'))
        {
            $orders->paginate($request->query('per_page'));
        }
        $orders->orderBy('shippedAt', 'desc');

        return $orders;
    }





}