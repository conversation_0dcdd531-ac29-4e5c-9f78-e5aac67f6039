<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\OrderController;

// Mixed access routes (tenant + JWT users)
Route::middleware('auth')->group(function () {
    Route::get('/orders/{id}', [OrderController::class, 'show']);
    Route::get('/collection/{collectionId}/orders', [OrderController::class, 'index']);
    Route::put('/orders/{id}/shipped', [OrderController::class, 'setShipped']);
});

// Tenant-only routes
Route::middleware('tenant.token')->group(function () {
    Route::prefix('orders')->group(function () {
        Route::post('/{collectionId}', [OrderController::class, 'store']);
        Route::put('/{id}', [OrderController::class, 'update']);
        Route::delete('/{id}', [OrderController::class, 'destroy']);
    });
});
