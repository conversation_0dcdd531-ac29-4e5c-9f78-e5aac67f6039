<?php

namespace App\Services;

use App\Models\Warehouse;
use App\Models\Tenant;
use App\Models\User;

class WarehouseService
{
    protected $tenantService;

    public function __construct(TenantService $tenantService)
    {
        $this->tenantService = $tenantService;
    }

    /**
     * Get all warehouses for tenant/user
     */
    public function getWarehouses($request): array
    {
        $tenant = $this->tenantService->getTenantFromRequest($request);
        $user = $this->tenantService->getUserFromRequest($request);

        if (!$tenant) {
            return [
                'success' => false,
                'message' => 'Unauthorized',
                'status' => 401
            ];
        }

        $query = $tenant->warehouses();

        // If user request, only show active warehouses
        if ($user) {
            $query->where('status', 'active');
        }

        $warehouses = $query->get();

        // Format response for users
        if ($user) {
            $warehouses = collect($this->tenantService->formatCollectionForUser($warehouses, 'warehouse'));
        }

        return [
            'success' => true,
            'data' => $warehouses
        ];
    }

    /**
     * Create a new warehouse
     */
    public function createWarehouse(array $data, Tenant $tenant): array
    {
        try {
            $warehouse = $tenant->warehouses()->create($data);

            return [
                'success' => true,
                'data' => $warehouse,
                'message' => 'Warehouse created successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create warehouse: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Update a warehouse
     */
    public function updateWarehouse(int $warehouseId, array $data, Tenant $tenant): array
    {
        $warehouse = Warehouse::where('tenant_id', $tenant->id)->find($warehouseId);

        if (!$warehouse) {
            return [
                'success' => false,
                'message' => 'Warehouse not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $warehouse->update($data);

            return [
                'success' => true,
                'data' => $warehouse,
                'message' => 'Warehouse updated successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update warehouse: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Delete a warehouse
     */
    public function deleteWarehouse(int $warehouseId, Tenant $tenant): array
    {
        $warehouse = Warehouse::where('tenant_id', $tenant->id)->find($warehouseId);

        if (!$warehouse) {
            return [
                'success' => false,
                'message' => 'Warehouse not found or unauthorized',
                'status' => 404
            ];
        }

        try {
            $warehouse->delete();

            return [
                'success' => true,
                'message' => 'Warehouse deleted successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to delete warehouse: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }
}
