<?php

namespace App\Http\Controllers;

use App\Models\Collection;
use App\Models\Warehouse;
use App\Services\CollectionsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CollectionController extends Controller
{
    private $collectionsService;

    //constructor
    public function __construct()
    {
        $this->collectionsService = new CollectionsService();
    }

    /**
     * List all collections for a specific warehouse (tenant-scoped).
     */
    public function index(Request $request, $warehouseId)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');

        // Make sure the warehouse belongs to the tenant
        $warehouse = $user ? $user->tenant->warehouses()->find($warehouseId) :
                             $tenant->warehouses()->find($warehouseId);
        if (!$warehouse) {
            return response()->json([
                'response' => 'error',
                'message' => 'Warehouse not found or unauthorized'], 404);
        }

        $collections = $warehouse->collections()->withCount('orders')->get();
        if($user)
        {
            $collections = $collections->map(function ($collection) {
                return [
                    'id' => $collection->id,
                    'num' => $collection->num,
                    'status' => $collection->status,
                    'addedAt' => $collection->added_at,
                    'shippingType' => $collection->shipping_type,
                    'shippingBy' => $collection->shipped_by,
                    'orders' => $collection->orders()->count(),
                ];
            });
        }

        return response()->json($collections);
    }

    /**
     * Create a new collection under a warehouse.
     */
    public function store(Request $request, $warehouseId)
    {
        $tenant = $request->get('tenant');

        $warehouse = $tenant->warehouses()->find($warehouseId);
        if (!$warehouse) {
            return response()->json([
                'response' => 'error',
                'message' => 'Warehouse not found or unauthorized'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'num' => 'required|string|max:255',
            'added_at' => 'required|date',
            'status' => 'required|string',
            'shipping_type' => 'required|string',
            'shipped_at' => 'nullable|date',
            'shipped_by' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        $collection = $warehouse->collections()->create($data);

        return response()->json($collection, 201);
    }

    /**
     * Update a STATUS OF collection.
     */
    public function updateStatus(Request $request, $id)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');


        $collection = $this->collectionsService->getCollectionsById($tenant, $user,$id);

        $validator = Validator::make($request->all(), [
            'status' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        $collection->update($data);

        return response()->json($collection);
    }

    /**
     * Show details of a single collection.
     */
    public function show(Request $request, $id)
    {
        $tenant = $request->get('tenant');
        $user = $request->get('user');


        $collection = Collection::withCount('orders')
            ->whereHas('warehouse', function ($q) use ($tenant, $user) {
                if ($tenant) {
                    $q->where('tenant_id', $tenant->id);
                }
                if ($user) {
                    $q->where('tenant_id', $user->tenant->id);
                    $q->where('status', 'active');
                }
            })
            ->find($id);
            if (!$collection) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Collection not found or unauthorized'], 404);
            }

            if($user)
            {
               return [
                        'id' => $collection->id,
                        'num' => $collection->num,
                        'status' => $collection->status,
                        'addedAt' => $collection->added_at,
                        'shippingType' => $collection->shipping_type,
                        'shippingBy' => $collection->shipped_by,
                        'orders' => $collection->orders()->count(),
                    ];

            }

        return response()->json($collection, 200);
    }

    /**
     * Update a collection.
     */
    public function update(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })
            ->find($id);
            if (!$collection) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Collection not found or unauthorized'], 404);
            }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'num' => 'sometimes|string|max:255',
            'added_at' => 'sometimes|date',
            'status' => 'sometimes|string',
            'shipping_type' => 'sometimes|string',
            'shipped_at' => 'nullable|date',
            'shipped_by' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        $collection->update($data);

        return response()->json($collection);
    }

    /**
     * Delete a collection.
     */
    public function destroy(Request $request, $id)
    {
        $tenant = $request->get('tenant');

        $collection = Collection::whereHas('warehouse', function ($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->find($id);
        if (!$collection) {
            return response()->json([
                'response' => 'error',
                'message' => 'Collection not found or unauthorized'], 404);
        }

        $collection->delete();

        return response()->json(['message' => 'Collection deleted']);
    }
}