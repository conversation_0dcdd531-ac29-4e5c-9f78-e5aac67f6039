<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Tenant;
use Illuminate\Support\Str;

class TenantController extends Controller
{

public function store(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'domain' => 'required|string|unique:tenants,domain',
        'logo' => 'nullable|image|mimes:jpg,jpeg,png,svg|max:2048',
    ]);

    // Handle logo upload
    $logoPath = null;
    if ($request->hasFile('logo')) {
        $logoPath = $request->file('logo')->store('uploads', 'public');
    }

    $tenant = Tenant::create([
        'name' => $request->name,
        'domain' => $request->domain,
        'logo' => $logoPath,
        'token' => Str::random(64),
    ]);

    return response()->json($tenant, 201);
}
}
