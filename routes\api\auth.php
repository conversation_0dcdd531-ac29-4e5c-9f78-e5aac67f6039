<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;

// Public authentication routes
Route::post('/login', [AuthController::class, 'login']);
Route::post('tenants/register', [AuthController::class, 'registerTenant']); //todelete after

// Tenant registration route
Route::middleware('tenant.token')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
});

// JWT authenticated user routes
Route::prefix('user')->middleware('jwt.auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/profile', [AuthController::class, 'profile']);
});
