<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RecreateOrdersTable extends Migration
{
    public function up()
    {
        // Step 1: Drop products table (it references orders)
        Schema::dropIfExists('products');

        // Step 2: Drop orders table
        Schema::dropIfExists('orders');

        // Step 3: Create orders table
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('collection_id')->constrained()->onDelete('cascade');

            $table->string('orderCode')->unique()->nullable();
            $table->string('orderNum')->unique()->nullable();
            $table->string('trackingNumber')->nullable();
            $table->string('shippingCompany')->nullable();
            $table->string('store')->nullable();
            $table->string('sku')->nullable();
            $table->text('goodsDescription')->nullable();
            $table->decimal('goodsValue', 10, 2)->nullable();
            $table->string('productLink')->nullable();
            $table->string('currency', 10)->nullable();
            $table->string('paymentMethod')->nullable();
            $table->string('appliedOffer')->nullable();
            $table->boolean('isUpsell')->nullable();
            $table->string('status')->nullable();
            $table->string('statusReason')->nullable();
            $table->timestamp('statusUpdatedAt')->nullable();
            $table->boolean('isConfirmed')->nullable();
            $table->text('moreInfo')->nullable();
            $table->string('unconfirmedReason')->nullable();
            $table->text('unconfirmedDescription')->nullable();
            $table->text('description')->nullable();
            $table->string('validatedVia')->nullable();
            $table->boolean('isexpired')->nullable();

            $table->string('followupStatus')->nullable();
            $table->timestamp('followupCreated')->nullable();
            $table->timestamp('followupSchedule')->nullable();
            $table->string('followupRejectReason')->nullable();
            $table->text('followupComment')->nullable();
            $table->timestamp('followupUpdatedAt')->nullable();

            $table->string('originCountry')->nullable();
            $table->string('destinationCountry')->nullable();

            $table->timestamp('shippedAt')->nullable();
            $table->timestamp('returnedAt')->nullable();
            $table->timestamp('deliveredAt')->nullable();

            $table->string('remittanceInvoice')->nullable();
            $table->string('shippingInvoice')->nullable();
            $table->string('callcenterInvoice')->nullable();

            $table->string('contact')->nullable();
            $table->string('mobileNumber')->nullable();
            $table->string('whatsappNumber')->nullable();
            $table->string('phoneNumber')->nullable();
            $table->string('language')->nullable();
            $table->string('country')->nullable();
            $table->text('address')->nullable();
            $table->unsignedBigInteger('cityId')->nullable();
            $table->string('city')->nullable();
            $table->string('province')->nullable();
            $table->string('shortAddress')->nullable();
            $table->string('houseNumber')->nullable();
            $table->string('nearestPlace')->nullable();
            $table->string('street')->nullable();
            $table->string('area')->nullable();
            $table->string('zipcode')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();

            $table->timestamps();
        });

        // Step 4: Recreate products table
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');

            $table->unsignedBigInteger('sellerId')->nullable();
            $table->string('sellerName')->nullable();
            $table->string('name')->nullable();
            $table->string('arabicName')->nullable();
            $table->string('sku')->nullable();
            $table->decimal('weight', 8, 2)->nullable();
            $table->decimal('width', 8, 2)->nullable();
            $table->decimal('height', 8, 2)->nullable();
            $table->decimal('length', 8, 2)->nullable();
            $table->string('status')->nullable();
            $table->boolean('isArchive')->default(false);
            $table->text('descriptionCallcenter')->nullable();
            $table->string('productLink')->nullable();
            $table->string('productVideo')->nullable();
            $table->unsignedBigInteger('warehouseId')->nullable();
            $table->string('warehouseName')->nullable();
            $table->string('shippingPriceType')->nullable();
            $table->string('shippingBy')->nullable();
            $table->string('confirmedBy')->nullable();
            $table->timestamp('confirmedAt')->nullable();
            $table->string('createdBy')->nullable();
            $table->string('updatedBy')->nullable();
            $table->text('listeStock')->nullable();
            $table->string('productType')->nullable();
            $table->string('shippingType')->nullable();
            $table->integer('quantity')->nullable();
            $table->unsignedBigInteger('parent')->nullable();
            $table->string('hscode')->nullable();
            $table->string('category')->nullable();
            $table->text('returnImages')->nullable();
            $table->string('returnReason')->nullable();

            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('products');
        Schema::dropIfExists('orders');
    }
}