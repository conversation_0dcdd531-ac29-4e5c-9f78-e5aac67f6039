<?php

namespace App\Services;

use App\Models\Tenant;
use App\Models\User;

class TenantService
{
    /**
     * Check if resource belongs to tenant
     */
    public function checkTenantAccess($resource, Tenant $tenant): bool
    {
        if (method_exists($resource, 'tenant')) {
            return $resource->tenant_id === $tenant->id;
        }

        if (method_exists($resource, 'warehouse')) {
            return $resource->warehouse->tenant_id === $tenant->id;
        }

        if (method_exists($resource, 'collection')) {
            return $resource->collection->warehouse->tenant_id === $tenant->id;
        }

        if (method_exists($resource, 'order')) {
            return $resource->order->collection->warehouse->tenant_id === $tenant->id;
        }

        return false;
    }

    /**
     * Check if resource belongs to user's tenant
     */
    public function checkUserAccess($resource, User $user): bool
    {
        return $this->checkTenantAccess($resource, $user->tenant);
    }

    /**
     * Get tenant from request (either direct tenant or user's tenant)
     */
    public function getTenantFromRequest($request): ?Tenant
    {
        if ($tenant = $request->get('tenant')) {
            return $tenant;
        }

        if ($user = $request->get('user')) {
            return $user->tenant;
        }

        return null;
    }

    /**
     * Get user from request (only if JWT authenticated)
     */
    public function getUserFromRequest($request): ?User
    {
        return $request->get('user');
    }

    /**
     * Check if request is from tenant (not user)
     */
    public function isTenantRequest($request): bool
    {
        return $request->has('tenant') && !$request->has('user');
    }

    /**
     * Check if request is from user (JWT authenticated)
     */
    public function isUserRequest($request): bool
    {
        return $request->has('user');
    }

    /**
     * Format response for user (limited fields)
     */
    public function formatForUser($data, string $type): array
    {
        switch ($type) {
            case 'collection':
                return [
                    'id' => $data->id,
                    'name' => $data->name,
                    'num' => $data->num,
                    'status' => $data->status,
                    'orders' => $data->orders()->count(),
                ];

            case 'order':
                return [
                    'id' => $data->id,
                    'orderCode' => $data->orderCode,
                    'orderNum' => $data->orderNum,
                    'goodsDescription' => $data->goodsDescription,
                    'status' => $data->status,
                    'ordersProducts' => $data->products->map(function ($product) {
                        return [
                            'id' => (int) $product->id,
                            'name' => $product->name,
                            'sku' => $product->sku,
                            'status' => $product->status,
                            'quantity' => (int) $product->quantity,
                        ];
                    }),
                ];

            case 'product':
                return [
                    'id' => $data->id,
                    'name' => $data->name,
                    'sku' => $data->sku,
                    'status' => $data->status,
                    'quantity' => $data->quantity,
                    'weight' => $data->weight,
                ];

            case 'warehouse':
                return [
                    'id' => $data->id,
                    'name' => $data->name,
                    'location' => $data->location,
                    'status' => $data->status,
                ];

            default:
                return $data->toArray();
        }
    }

    /**
     * Format collection of data for user
     */
    public function formatCollectionForUser($collection, string $type): array
    {
        return $collection->map(function ($item) use ($type) {
            return $this->formatForUser($item, $type);
        })->toArray();
    }
}
