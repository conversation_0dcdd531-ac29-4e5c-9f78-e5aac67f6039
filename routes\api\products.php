<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductController;

// Mixed access routes (tenant + JWT users)
Route::middleware('auth')->group(function () {
    Route::get('/products/{id}', [ProductController::class, 'show']);
    Route::get('/order/{orderId}/products', [ProductController::class, 'index']);
    Route::put('/products/{id}/gather', [ProductController::class, 'gather']);
});

// Tenant-only routes
Route::middleware('tenant.token')->group(function () {
    // Product gather by SKU (outside prefix to avoid conflicts)
    Route::put('/{sku}', [ProductController::class, 'gatherBySku']);

    Route::prefix('products')->group(function () {
        Route::post('/{orderId}', [ProductController::class, 'store']);
        Route::put('/{id}', [ProductController::class, 'update']);
        Route::delete('/{id}', [ProductController::class, 'destroy']);
    });
});
