<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ReturnsController;

// JWT authenticated user routes only (returns are user-specific)
Route::middleware('jwt.auth')->group(function () {
    Route::prefix('returns')->group(function () {
        Route::get('/', [ReturnsController::class, 'index']);
        Route::get('/{id}', [ReturnsController::class, 'show']);
        Route::post('/', [ReturnsController::class, 'store']);
        Route::put('/{id}/status', [ReturnsController::class, 'updateStatus']);
        Route::delete('/{id}', [ReturnsController::class, 'destroy']);
    });
});
