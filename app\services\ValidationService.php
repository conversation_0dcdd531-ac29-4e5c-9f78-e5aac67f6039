<?php

namespace App\Services;

use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ValidationService
{
    /**
     * Validate tenant registration data
     */
    public function validateTenantRegistration(array $data): array
    {
        $validator = Validator::make($data, [
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:tenants,domain',
            'logo' => 'nullable|string|max:255',
        ]);

        return $this->handleValidation($validator);
    }

    /**
     * Validate user registration data
     */
    public function validateUserRegistration(array $data, int $tenantId): array
    {
        $validator = Validator::make($data, [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users')->where(function ($query) use ($tenantId) {
                    return $query->where('tenant_id', $tenantId);
                }),
            ],
            'password' => 'required|string|min:8',
        ]);

        return $this->handleValidation($validator);
    }

    /**
     * Validate user login data
     */
    public function validateUserLogin(array $data): array
    {
        $validator = Validator::make($data, [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        return $this->handleValidation($validator);
    }

    /**
     * Validate warehouse data
     */
    public function validateWarehouse(array $data, bool $isUpdate = false): array
    {
        $rules = [
            'name' => ($isUpdate ? 'sometimes' : 'required') . '|string|max:255',
            'location' => ($isUpdate ? 'sometimes' : '') . '|string|max:255',
            'status' => ($isUpdate ? 'sometimes' : 'required') . '|string|max:255',
        ];

        $validator = Validator::make($data, $rules);
        return $this->handleValidation($validator);
    }

    /**
     * Validate collection data
     */
    public function validateCollection(array $data, bool $isUpdate = false): array
    {
        $rules = [
            'name' => ($isUpdate ? 'sometimes' : 'required') . '|string|max:255',
            'num' => ($isUpdate ? 'sometimes' : 'required') . '|string|max:255',
            'added_at' => ($isUpdate ? 'sometimes' : 'required') . '|date',
            'status' => ($isUpdate ? 'sometimes' : 'required') . '|string',
            'shipping_type' => ($isUpdate ? 'sometimes' : 'required') . '|string',
            'shipped_at' => 'nullable|date',
            'shipped_by' => 'nullable|string|max:255',
        ];

        $validator = Validator::make($data, $rules);
        return $this->handleValidation($validator);
    }

    /**
     * Validate order data
     */
    public function validateOrder(array $data, bool $isUpdate = false, ?int $orderId = null): array
    {
        $rules = [
            'orderCode' => ($isUpdate ? 'sometimes' : 'required') . '|unique:orders,orderCode' . ($orderId ? ',' . $orderId : '') . '|string|max:255',
            'orderNum' => ($isUpdate ? 'sometimes' : 'required') . '|unique:orders,orderNum' . ($orderId ? ',' . $orderId : '') . '|string|max:255',
            'trackingNumber' => 'nullable|string|max:255',
            'shippingCompany' => 'nullable|string|max:255',
            'store' => 'nullable|string|max:255',
            'sku' => 'nullable|string|max:255',
            'goodsDescription' => 'nullable|string',
            'goodsValue' => 'nullable|numeric',
            'productLink' => 'nullable|url',
            'currency' => 'nullable|string|max:10',
            'paymentMethod' => 'nullable|string|max:255',
            'appliedOffer' => 'nullable|string|max:255',
            'isUpsell' => 'nullable|boolean',
            'status' => ($isUpdate ? 'sometimes' : 'required') . '|string|max:255',
            'statusReason' => 'nullable|string',
            'isConfirmed' => 'nullable|boolean',
            'moreInfo' => 'nullable|string',
            'unconfirmedReason' => 'nullable|string',
            'unconfirmedDescription' => 'nullable|string',
            'description' => 'nullable|string',
            'validatedVia' => 'nullable|string|max:255',
            'isexpired' => 'nullable|boolean',
            'followupStatus' => 'nullable|string|max:255',
            'followupRejectReason' => 'nullable|string',
            'followupComment' => 'nullable|string',
            'originCountry' => 'nullable|string|max:255',
            'destinationCountry' => 'nullable|string|max:255',
            'contact' => 'nullable|string|max:255',
            'mobileNumber' => 'nullable|string|max:255',
            'whatsappNumber' => 'nullable|string|max:255',
            'phoneNumber' => 'nullable|string|max:255',
            'language' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'cityId' => 'nullable|integer',
            'city' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',
            'shortAddress' => 'nullable|string|max:255',
            'houseNumber' => 'nullable|string|max:255',
            'nearestPlace' => 'nullable|string|max:255',
            'street' => 'nullable|string|max:255',
            'area' => 'nullable|string|max:255',
            'zipcode' => 'nullable|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
        ];

        $validator = Validator::make($data, $rules);
        return $this->handleValidation($validator);
    }

    /**
     * Validate product data
     */
    public function validateProduct(array $data, bool $isUpdate = false, ?int $productId = null): array
    {
        $rules = [
            'sellerId' => 'nullable|integer',
            'sellerName' => 'nullable|string|max:255',
            'name' => 'nullable|string|max:255',
            'arabicName' => 'nullable|string|max:255',
            'sku' => ($isUpdate ? 'sometimes' : 'required') . '|string|max:255|unique:products,sku' . ($productId ? ',' . $productId : ''),
            'weight' => 'nullable|numeric',
            'width' => 'nullable|numeric',
            'height' => 'nullable|numeric',
            'length' => 'nullable|numeric',
            'status' => 'nullable|string|max:255',
            'isArchive' => 'nullable|boolean',
            'descriptionCallcenter' => 'nullable|string',
            'productLink' => 'nullable|url',
            'productVideo' => 'nullable|url',
            'warehouseId' => 'nullable|integer',
            'warehouseName' => 'nullable|string|max:255',
            'shippingPriceType' => 'nullable|string|max:255',
            'shippingBy' => 'nullable|string|max:255',
            'confirmedBy' => 'nullable|string|max:255',
            'createdBy' => 'nullable|string|max:255',
            'updatedBy' => 'nullable|string|max:255',
            'listeStock' => 'nullable|json',
            'productType' => 'nullable|string|max:255',
            'shippingType' => 'nullable|string|max:255',
            'quantity' => 'nullable|integer',
            'parent' => 'nullable|string|max:255',
            'hscode' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'returnImages' => 'nullable|string',
            'returnReason' => 'nullable|string',
        ];

        $validator = Validator::make($data, $rules);
        return $this->handleValidation($validator);
    }

    /**
     * Handle validation result
     */
    private function handleValidation($validator): array
    {
        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation failed'
            ];
        }

        return [
            'success' => true,
            'data' => $validator->validated()
        ];
    }
}
