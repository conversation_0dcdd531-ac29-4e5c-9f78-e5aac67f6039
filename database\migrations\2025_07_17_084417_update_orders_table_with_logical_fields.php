<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'orderCode')) $table->string('orderCode')->nullable();
            if (!Schema::hasColumn('orders', 'orderNum')) $table->string('orderNum')->nullable();
            if (!Schema::hasColumn('orders', 'trackingNumber')) $table->string('trackingNumber')->nullable();
            if (!Schema::hasColumn('orders', 'shippingCompany')) $table->string('shippingCompany')->nullable();
            if (!Schema::hasColumn('orders', 'store')) $table->string('store')->nullable();
            if (!Schema::hasColumn('orders', 'sku')) $table->string('sku')->nullable();
            if (!Schema::hasColumn('orders', 'goodsDescription')) $table->text('goodsDescription')->nullable();
            if (!Schema::hasColumn('orders', 'goodsValue')) $table->decimal('goodsValue', 10, 2)->nullable();
            if (!Schema::hasColumn('orders', 'productLink')) $table->string('productLink')->nullable();
            if (!Schema::hasColumn('orders', 'currency')) $table->string('currency')->nullable();
            if (!Schema::hasColumn('orders', 'paymentMethod')) $table->string('paymentMethod')->nullable();
            if (!Schema::hasColumn('orders', 'appliedOffer')) $table->string('appliedOffer')->nullable();
            if (!Schema::hasColumn('orders', 'isUpsell')) $table->boolean('isUpsell')->nullable();
            // Skip 'status' since it already exists
            if (!Schema::hasColumn('orders', 'statusReason')) $table->string('statusReason')->nullable();
            if (!Schema::hasColumn('orders', 'statusUpdatedAt')) $table->timestamp('statusUpdatedAt')->nullable();
            if (!Schema::hasColumn('orders', 'isConfirmed')) $table->boolean('isConfirmed')->nullable();
            if (!Schema::hasColumn('orders', 'moreInfo')) $table->text('moreInfo')->nullable();
            if (!Schema::hasColumn('orders', 'unconfirmedReason')) $table->text('unconfirmedReason')->nullable();
            if (!Schema::hasColumn('orders', 'unconfirmedDescription')) $table->text('unconfirmedDescription')->nullable();
            if (!Schema::hasColumn('orders', 'description')) $table->text('description')->nullable();
            if (!Schema::hasColumn('orders', 'validatedVia')) $table->string('validatedVia')->nullable();
            if (!Schema::hasColumn('orders', 'isexpired')) $table->boolean('isexpired')->nullable();
            if (!Schema::hasColumn('orders', 'followupStatus')) $table->string('followupStatus')->nullable();
            if (!Schema::hasColumn('orders', 'followupCreated')) $table->timestamp('followupCreated')->nullable();
            if (!Schema::hasColumn('orders', 'followupSchedule')) $table->timestamp('followupSchedule')->nullable();
            if (!Schema::hasColumn('orders', 'followupRejectReason')) $table->text('followupRejectReason')->nullable();
            if (!Schema::hasColumn('orders', 'followupComment')) $table->text('followupComment')->nullable();
            if (!Schema::hasColumn('orders', 'followupUpdatedAt')) $table->timestamp('followupUpdatedAt')->nullable();
            if (!Schema::hasColumn('orders', 'originCountry')) $table->string('originCountry')->nullable();
            if (!Schema::hasColumn('orders', 'destinationCountry')) $table->string('destinationCountry')->nullable();
            if (!Schema::hasColumn('orders', 'shippedAt')) $table->timestamp('shippedAt')->nullable();
            if (!Schema::hasColumn('orders', 'returnedAt')) $table->timestamp('returnedAt')->nullable();
            if (!Schema::hasColumn('orders', 'deliveredAt')) $table->timestamp('deliveredAt')->nullable();
            if (!Schema::hasColumn('orders', 'remittanceInvoice')) $table->string('remittanceInvoice')->nullable();
            if (!Schema::hasColumn('orders', 'shippingInvoice')) $table->string('shippingInvoice')->nullable();
            if (!Schema::hasColumn('orders', 'callcenterInvoice')) $table->string('callcenterInvoice')->nullable();
            if (!Schema::hasColumn('orders', 'contact')) $table->string('contact')->nullable();
            if (!Schema::hasColumn('orders', 'mobileNumber')) $table->string('mobileNumber')->nullable();
            if (!Schema::hasColumn('orders', 'whatsappNumber')) $table->string('whatsappNumber')->nullable();
            if (!Schema::hasColumn('orders', 'phoneNumber')) $table->string('phoneNumber')->nullable();
            if (!Schema::hasColumn('orders', 'language')) $table->string('language')->nullable();
            if (!Schema::hasColumn('orders', 'country')) $table->string('country')->nullable();
            if (!Schema::hasColumn('orders', 'address')) $table->string('address')->nullable();
            if (!Schema::hasColumn('orders', 'cityId')) $table->unsignedBigInteger('cityId')->nullable();
            if (!Schema::hasColumn('orders', 'city')) $table->string('city')->nullable();
            if (!Schema::hasColumn('orders', 'province')) $table->string('province')->nullable();
            if (!Schema::hasColumn('orders', 'shortAddress')) $table->string('shortAddress')->nullable();
            if (!Schema::hasColumn('orders', 'houseNumber')) $table->string('houseNumber')->nullable();
            if (!Schema::hasColumn('orders', 'nearestPlace')) $table->string('nearestPlace')->nullable();
            if (!Schema::hasColumn('orders', 'street')) $table->string('street')->nullable();
            if (!Schema::hasColumn('orders', 'area')) $table->string('area')->nullable();
            if (!Schema::hasColumn('orders', 'zipcode')) $table->string('zipcode')->nullable();
            if (!Schema::hasColumn('orders', 'latitude')) $table->decimal('latitude', 10, 7)->nullable();
            if (!Schema::hasColumn('orders', 'longitude')) $table->decimal('longitude', 10, 7)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'orderCode', 'orderNum', 'trackingNumber', 'shippingCompany', 'store', 'sku',
                'goodsDescription', 'goodsValue', 'productLink', 'currency', 'paymentMethod',
                'appliedOffer', 'isUpsell', 'status', 'statusReason', 'statusUpdatedAt',
                'isConfirmed', 'moreInfo', 'unconfirmedReason', 'unconfirmedDescription', 'description',
                'validatedVia', 'isexpired', 'followupStatus', 'followupCreated', 'followupSchedule',
                'followupRejectReason', 'followupComment', 'followupUpdatedAt', 'originCountry',
                'destinationCountry', 'shippedAt', 'returnedAt', 'deliveredAt', 'remittanceInvoice',
                'shippingInvoice', 'callcenterInvoice', 'contact', 'mobileNumber', 'whatsappNumber',
                'phoneNumber', 'language', 'country', 'address', 'cityId', 'city', 'province',
                'shortAddress', 'houseNumber', 'nearestPlace', 'street', 'area', 'zipcode',
                'latitude', 'longitude'
            ]);
        });
    }

};